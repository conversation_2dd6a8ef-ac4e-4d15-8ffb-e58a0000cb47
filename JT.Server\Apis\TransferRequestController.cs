using JT.Server.Entities;
using Microsoft.AspNetCore.Mvc;

namespace JT.Server.Apis;

/// <summary>
/// Controller for managing transfer requests.
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class TransferRequestController : JTControllerBase
{
    private readonly string _transferRequestsFilePath = "TransferRequests.json";
    private readonly string _savedTransferRequestsFilePath = "SavedTransferRequests.json";
    private readonly string _jobCategoriesFilePath = "JobCategories.json";
    private readonly string _industriesFilePath = "Industries.json";


    /// <summary>
    /// Logger for the TransferRequestController.
    /// </summary>
    private readonly ILogger<TransferRequestController> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="TransferRequestController"/> class.
    /// </summary>
    /// <param name="logger">The logger instance for logging operations.</param>
    public TransferRequestController(ILogger<TransferRequestController> logger) => _logger = logger;


   
    /// <summary>
    /// Retrieves all transfer requests.
    /// </summary>
    /// <returns>A collection of transfer requests.</returns>
    [HttpGet("transferRequest")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(IEnumerable<TransferRequestData>), 200)]
    [ProducesResponseType(404)]
    public ActionResult<IEnumerable<TransferRequestData>> GetTransferRequests()
    {
        try
        {
            var transferRequests = LoadData<List<TransferRequestData>>(_transferRequestsFilePath);
            return Ok(transferRequests.ToImmutableList());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting transfer requests");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Retrieves a specific transfer request by its unique identifier.
    /// </summary>
    /// <param name="id">The unique identifier of the transfer request to retrieve.</param>
    /// <returns>The transfer request data if found, or a 404 Not Found response if not found.</returns>
    [HttpGet("{id:guid}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(TransferRequestData), 200)]
    [ProducesResponseType(404)]
    public ActionResult<TransferRequestData> GetTransferRequest(Guid id)
    {
        try
        {
            var transferRequests = LoadData<List<TransferRequestData>>(_transferRequestsFilePath);
            var transferRequest = transferRequests.FirstOrDefault(r => r.Id == id);

            if (transferRequest == null)
            {
                return NotFound();
            }

            return Ok(transferRequest);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting transfer request {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Creates a new transfer request.
    /// </summary>
    /// <param name="transferRequest">The transfer request data to create.</param>
    /// <returns>The created transfer request with its generated ID and metadata.</returns>
    [HttpPost("createtransferRequest")]
    [ProducesResponseType(typeof(TransferRequestData), 200)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<TransferRequestData>> CreateTransferRequest(TransferRequestData transferRequest)
    {
        try
        {
            transferRequest.Id = Guid.NewGuid();
            transferRequest.SubmissionDate = DateTime.UtcNow;
            transferRequest.Status = "Pending";
            transferRequest.ViewCount = 0;
            transferRequest.InterestedEmployers = 0;

            var transferRequests = LoadData<List<TransferRequestData>>(_transferRequestsFilePath);
            transferRequests.Add(transferRequest);
            await SaveMockData(_transferRequestsFilePath, transferRequests);

            return CreatedAtAction(nameof(GetTransferRequest), new { id = transferRequest.Id }, transferRequest);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating transfer request");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Updates an existing transfer request.
    /// </summary>
    /// <param name="id">The unique identifier of the transfer request to update.</param>
    /// <param name="transferRequest">The updated transfer request data.</param>
    /// <returns>The updated transfer request if successful, or an appropriate error response.</returns>
    [HttpPut("{id:guid}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(TransferRequestData), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<TransferRequestData>> UpdateTransferRequest(Guid id, TransferRequestData transferRequest)
    {
        try
        {
            var transferRequests = LoadData<List<TransferRequestData>>(_transferRequestsFilePath);
            var existingIndex = transferRequests.FindIndex(r => r.Id == id);

            if (existingIndex == -1)
            {
                return NotFound();
            }

            transferRequest.Id = id;
            transferRequests[existingIndex] = transferRequest;
            await SaveMockData(_transferRequestsFilePath, transferRequests);

            return Ok(transferRequest);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating transfer request {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Deletes an existing transfer request.
    /// </summary>
    /// <param name="id">The unique identifier of the transfer request to delete.</param>
    /// <returns>No content if successful, or an appropriate error response.</returns>
    [HttpDelete("{id:guid}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(TransferRequestData), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult> DeleteTransferRequest(Guid id)
    {
        try
        {
            var transferRequests = LoadData<List<TransferRequestData>>(_transferRequestsFilePath);
            var existingIndex = transferRequests.FindIndex(r => r.Id == id);

            if (existingIndex == -1)
            {
                return NotFound();
            }

            transferRequests.RemoveAt(existingIndex);
            await SaveMockData(_transferRequestsFilePath, transferRequests);

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting transfer request {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Retrieves all transfer requests associated with a specific user.
    /// </summary>
    /// <param name="userId">The unique identifier of the user whose transfer requests are to be retrieved.</param>
    /// <returns>A collection of transfer requests associated with the specified user.</returns>
    [HttpGet("user/{userId:guid}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(TransferRequestData), 200)]
    [ProducesResponseType(404)]
    public ActionResult<IEnumerable<TransferRequestData>> GetUserTransferRequests(Guid userId)
    {
        try
        {
            var transferRequests = LoadData<List<TransferRequestData>>(_transferRequestsFilePath);
            var userRequests = transferRequests.Where(r => r.UserId == userId).ToList();
            return Ok(userRequests);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting transfer requests for user {UserId}", userId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Retrieves transfer requests filtered by their status.
    /// </summary>
    /// <param name="status">The status to filter transfer requests by (e.g., "Pending", "Approved").</param>
    /// <returns>A collection of transfer requests matching the specified status.</returns>
    [HttpGet("status/{status}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(TransferRequestData), 200)]
    [ProducesResponseType(404)]
    public ActionResult<IEnumerable<TransferRequestData>> GetTransferRequestsByStatus(string status)
    {
        try
        {
            var transferRequests = LoadData<List<TransferRequestData>>(_transferRequestsFilePath);
            var filteredRequests = transferRequests.Where(r =>
                string.Equals(r.Status, status, StringComparison.OrdinalIgnoreCase)).ToList();
            return Ok(filteredRequests.ToImmutableList());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting transfer requests by status {Status}", status);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Increments the view count for a specific transfer request.
    /// </summary>
    /// <param name="id">The unique identifier of the transfer request.</param>
    /// <returns>An ActionResult indicating the result of the operation.</returns>
    [HttpPost("{id:guid}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(TransferRequestData), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult> IncrementViewCount(Guid id)
    {
        try
        {
            var transferRequests = LoadData<List<TransferRequestData>>(_transferRequestsFilePath);
            var existingIndex = transferRequests.FindIndex(r => r.Id == id);

            if (existingIndex == -1)
            {
                return NotFound();
            }

            transferRequests[existingIndex].ViewCount++;
            await SaveMockData(_transferRequestsFilePath, transferRequests);

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error incrementing view count for transfer request {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Retrieves favorited TransferRequest for a specific user.
    /// </summary>
    /// <param name="userId">The user ID.</param>
    /// <returns>A list of favorited TransferRequest.</returns>
    [HttpGet("favorited")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(IEnumerable<TransferRequestData>), 200)]
    public ActionResult<IEnumerable<TransferRequestData>> GetFavorited([FromQuery] Guid userId)
    {
        var savedRecipes = LoadData<List<Guid>>(_savedTransferRequestsFilePath);

        var recipes = LoadData<List<TransferRequestData>>(_transferRequestsFilePath);
        var favorited = recipes
            .Where(r => savedRecipes.Contains(r.Id))
            .Select(r =>
            {
                r.IsFavorite = true;
                return r;
            })
            .ToImmutableList();

        return Ok(favorited);
    }
}
