namespace JT.Content.Client.Mock;

public class MockTransferEndpoints(string basePath, ISerializer serializer, ILogger<BaseMockEndpoint> logger) : BaseMockEndpoint(serializer, logger)
{
	public async Task<string> HandleTransferRequestsRequest(HttpRequestMessage request)
	{
		var savedList = await LoadData<List<Guid>>("SavedTransferRequests.json") ?? [];
		var allTransferRequests = await LoadData<List<TransferRequestData>>("TransferRequests.json") ?? [];

		allTransferRequests.ForEach((_, r) => r.IsFavorite = savedList.Contains(r.Id ?? Guid.Empty));

		var path = request.RequestUri?.AbsolutePath ?? string.Empty;
		if (path.Contains("/api/JobCategory"))
		{
			return await HandleCategoriesRequest();
		}

		if (path.Contains("/api/TransferRequest/trending"))
		{
			return serializer.ToString(allTransferRequests.Take(10));
		}

		if (path.Contains("/api/TransferRequest/popular"))
		{
			return serializer.ToString(allTransferRequests.Take(10));
		}

		if (path.Contains("/api/TransferRequest/favorited"))
		{
			return serializer.ToString(allTransferRequests.Where(r => r.IsFavorite ?? false).ToList());
		}

		if (path.Contains("/steps"))
		{
			var segments = request.RequestUri?.Segments;
			var segmentId = segments?.Length >= 2 ? segments[^2] : string.Empty;
			return GetRecipeSteps(allTransferRequests, segmentId);
		}

		if (path.Contains("/ingredients"))
		{
			var segments = request.RequestUri?.Segments;
			var segmentId = segments?.Length >= 2 ? segments[^2] : string.Empty;
			return GetRecipeIngredients(allTransferRequests, segmentId);
		}

		if (path.Contains("/reviews"))
		{
			var segments = request.RequestUri?.Segments;
			var segmentId = segments?.Length >= 2 ? segments[^2] : string.Empty;
			return GetRecipeReviews(allTransferRequests, segmentId);
		}

		if (request.Method == HttpMethod.Get && path == "/api/TransferRequest/transferRequest")
		{
			return serializer.ToString(allTransferRequests);
		}

		if (request.Method == HttpMethod.Get && path == "/api/TransferRequest")
		{
			return serializer.ToString(allTransferRequests);
		}

		if (path.Contains("/api/TransferRequest/review/like"))
		{
			// Review functionality not applicable to transfer requests
			return "{}";
		}

		if (path.Contains("/api/TransferRequest/review/dislike"))
		{
			// Review functionality not applicable to transfer requests
			return "{}";
		}

		var lastSegment = request.RequestUri?.Segments?.LastOrDefault() ?? string.Empty;
		return GetRecipeDetails(allTransferRequests, lastSegment);
	}

	private string GetRecipeDetails(List<TransferRequestData> allTransferRequests, string recipeId)
	{
		recipeId = recipeId.TrimEnd('/');
		if (Guid.TryParse(recipeId, out var gid))
		{
			var recipe = allTransferRequests.FirstOrDefault(x => x.Id == gid);
			if (recipe != null)
			{
				return serializer.ToString(recipe);
			}
		}

		return "{}";
	}

	private async Task<string> HandleCategoriesRequest()
	{
		var allCategories = await LoadData<List<CategoryData>>("JobCategories.json") ?? [];
		return serializer.ToString(allCategories);
	}

	private static string GetRecipeSteps(List<TransferRequestData> allTransferRequests, string recipeId)
	{
		// Transfer requests don't have steps - return empty array
		_ = allTransferRequests; // Suppress unused parameter warning
		_ = recipeId; // Suppress unused parameter warning
		return "[]";
	}

	private static string GetRecipeIngredients(List<TransferRequestData> allTransferRequests, string recipeId)
	{
		// Transfer requests don't have ingredients - return empty array
		_ = allTransferRequests; // Suppress unused parameter warning
		_ = recipeId; // Suppress unused parameter warning
		return "[]";
	}

	private string GetRecipeReviews(List<TransferRequestData> allTransferRequests, string recipeId)
	{
		recipeId = recipeId.TrimEnd('/');

		if (Guid.TryParse(recipeId, out var parsedId))
		{
			var transferRequest = allTransferRequests.FirstOrDefault(r => r.Id == parsedId);
			if (transferRequest != null && transferRequest.Responses != null)
			{
				// Return employer responses as "reviews" for transfer requests
				return serializer.ToString(transferRequest.Responses);
			}
		}

		return "[]";
	}


}
