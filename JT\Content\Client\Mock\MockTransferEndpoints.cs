namespace JT.Content.Client.Mock;

public class MockTransferEndpoints(string basePath, ISerializer serializer, ILogger<BaseMockEndpoint> logger) : BaseMockEndpoint(serializer, logger)
{
	public async Task<string> HandleTransferRequestsRequest(HttpRequestMessage request)
	{
		var savedList = await LoadData<List<Guid>>("SavedTransferRequests.json") ?? [];
		var allTransferRequests = await LoadData<List<TransferRequestData>>("TransferRequests.json") ?? [];

		allTransferRequests.ForEach((_, r) => r.IsFavorite = savedList.Contains(r.Id ?? Guid.Empty));

		var path = request.RequestUri.AbsolutePath;
		if (path.Contains("/api/Recipe/categories"))
		{
			return await HandleCategoriesRequest();
		}

		if (path.Contains("/api/Recipe/trending"))
		{
			return serializer.ToString(allTransferRequests.Take(10));
		}

		if (path.Contains("/api/Recipe/popular"))
		{
			return serializer.ToString(allTransferRequests.Take(10));
		}

		if (path.Contains("/api/Recipe/favorited"))
		{
			return serializer.ToString(allTransferRequests.Where(r => r.IsFavorite ?? false).ToList());
		}

		if (path.Contains("/steps"))
		{
			return GetRecipeSteps(allTransferRequests, request.RequestUri.Segments[^2]);
		}

		if (path.Contains("/ingredients"))
		{
			return GetRecipeIngredients(allTransferRequests, request.RequestUri.Segments[^2]);
		}

		if (path.Contains("/reviews"))
		{
			return GetRecipeReviews(allTransferRequests, request.RequestUri.Segments[^2]);
		}

		if (request.Method == HttpMethod.Get && path == "/api/Recipe")
		{
			return serializer.ToString(allTransferRequests);
		}

		if (path.Contains("/api/Recipe/review/like"))
		{
			// Review functionality not applicable to transfer requests
			return "{}";
		}

		if (path.Contains("/api/Recipe/review/dislike"))
		{
			// Review functionality not applicable to transfer requests
			return "{}";
		}

		return GetRecipeDetails(allTransferRequests, request.RequestUri.Segments.Last());
	}

	private string GetRecipeDetails(List<TransferRequestData> allTransferRequests, string recipeId)
	{
		recipeId = recipeId.TrimEnd('/');
		if (Guid.TryParse(recipeId, out var gid))
		{
			var recipe = allTransferRequests.FirstOrDefault(x => x.Id == gid);
			if (recipe != null)
			{
				return serializer.ToString(recipe);
			}
		}

		return "{}";
	}

	private async Task<string> HandleCategoriesRequest()
	{
		var allCategories = await LoadData<List<CategoryData>>("JobCategories.json") ?? [];
		return serializer.ToString(allCategories);
	}

	private static string GetRecipeSteps(List<TransferRequestData> allTransferRequests, string recipeId)
	{
		// Transfer requests don't have steps - return empty array
		_ = allTransferRequests; // Suppress unused parameter warning
		_ = recipeId; // Suppress unused parameter warning
		return "[]";
	}

	private static string GetRecipeIngredients(List<TransferRequestData> allTransferRequests, string recipeId)
	{
		// Transfer requests don't have ingredients - return empty array
		_ = allTransferRequests; // Suppress unused parameter warning
		_ = recipeId; // Suppress unused parameter warning
		return "[]";
	}

	private string GetRecipeReviews(List<TransferRequestData> allTransferRequests, string recipeId)
	{
		recipeId = recipeId.TrimEnd('/');

		if (Guid.TryParse(recipeId, out var parsedId))
		{
			var transferRequest = allTransferRequests.FirstOrDefault(r => r.Id == parsedId);
			if (transferRequest != null && transferRequest.Responses != null)
			{
				// Return employer responses as "reviews" for transfer requests
				return serializer.ToString(transferRequest.Responses);
			}
		}

		return "[]";
	}


}
