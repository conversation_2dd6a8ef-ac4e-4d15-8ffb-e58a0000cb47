{"descriptionHash": "1B085A6E906489ABF195A91DB505AB7C84A979BC9D1C209DCD06567E188A14F4054C02B8D18DBFD55E0EADF8FDE42AE9133CF6C0BBC6D531722E1962750511FF", "descriptionLocation": "../../Specs/JTApiClient.swagger.json", "lockFileVersion": "1.0.0", "kiotaVersion": "1.27.0", "clientClassName": "JTApiClient", "typeAccessModifier": "Public", "clientNamespaceName": "JT.Content.Client", "language": "CSharp", "usesBackingStore": false, "excludeBackwardCompatible": false, "includeAdditionalData": true, "disableSSLValidation": false, "serializers": ["Microsoft.Kiota.Serialization.Json.JsonSerializationWriterFactory", "Microsoft.Kiota.Serialization.Text.TextSerializationWriterFactory", "Microsoft.Kiota.Serialization.Form.FormSerializationWriterFactory", "Microsoft.Kiota.Serialization.Multipart.MultipartSerializationWriterFactory"], "deserializers": ["Microsoft.Kiota.Serialization.Json.JsonParseNodeFactory", "Microsoft.Kiota.Serialization.Text.TextParseNodeFactory", "Microsoft.Kiota.Serialization.Form.FormParseNodeFactory"], "structuredMimeTypes": ["application/json", "text/plain;q=0.9", "application/x-www-form-urlencoded;q=0.2", "multipart/form-data;q=0.1"], "includePatterns": [], "excludePatterns": [], "disabledValidationRules": []}