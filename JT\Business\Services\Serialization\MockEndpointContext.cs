using System.Text.Json.Serialization;
using JT.Content.Client.Models;

namespace JT.Business.Services;

//[JsonSerializable(typeof(List<CookbookData>))]
//[JsonSerializable(typeof(CookbookData))]
//[JsonSerializable(typeof(RecipeData))]
//[JsonSerializable(typeof(List<NotificationData>))]
//[JsonSerializable(typeof(List<RecipeData>))]
[JsonSerializable(typeof(List<CategoryData>))]
[JsonSerializable(typeof(CategoryData))]
//[JsonSerializable(typeof(List<IngredientData>))]
[JsonSerializable(typeof(List<UserData>))]
//[JsonSerializable(typeof(List<StepData>))]
//[JsonSerializable(typeof(List<ReviewData>))]
[JsonSerializable(typeof(UserData))]
[JsonSerializable(typeof(Guid))]
//[JsonSerializable(typeof(ReviewData))]
//[JsonSerializable(typeof(IEnumerable<RecipeData>))]
//[JsonSerializable(typeof(TimeSpanObject))]
[JsonSerializable(typeof(LoginRequest))]
// Job Transfer specific data types
[JsonSerializable(typeof(List<TransferRequestData>))]
[JsonSerializable(typeof(TransferRequestData))]
//[JsonSerializable(typeof(List<SubscriptionPlanData>))]
//[JsonSerializable(typeof(SubscriptionPlanData))]
//[JsonSerializable(typeof(List<TokenTransactionData>))]
//[JsonSerializable(typeof(TokenTransactionData))]
//[JsonSerializable(typeof(List<PaymentTransactionData>))]
//[JsonSerializable(typeof(PaymentTransactionData))]
//[JsonSerializable(typeof(List<SkillData>))]
//[JsonSerializable(typeof(SkillData))]
//[JsonSerializable(typeof(List<ReferralData>))]
//[JsonSerializable(typeof(ReferralData))]
//[JsonSerializable(typeof(List<AnalyticsData>))]
//[JsonSerializable(typeof(AnalyticsData))]
//[JsonSerializable(typeof(List<OrganizationData>))]
//[JsonSerializable(typeof(OrganizationData))]
//[JsonSerializable(typeof(List<SavedCookbooksData>))]
//[JsonSerializable(typeof(List<SavedRecipesData>))]
//[JsonSerializable(typeof(IEnumerable<SavedRecipesData>))]
//[JsonSerializable(typeof(SavedRecipesData))]
[JsonSourceGenerationOptions(PropertyNamingPolicy = JsonKnownNamingPolicy.CamelCase, PropertyNameCaseInsensitive = true)]
public partial class MockEndpointContext : JsonSerializerContext
{
}
