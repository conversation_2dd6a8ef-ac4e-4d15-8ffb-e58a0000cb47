namespace JT.Content.Client.Mock;

public class MockJobTransferEndpoints(string basePath, ISerializer serializer, ILogger<BaseMockEndpoint> logger) : BaseMockEndpoint(serializer, logger)
{
	public async Task<string> HandleJobTransferRequest(HttpRequestMessage request)
	{
		var path = request.RequestUri?.AbsolutePath ?? string.Empty;

		// Handle Skills
		if (path.Contains("/api/Skill"))
		{
			var skills = await LoadData<List<SkillData>>("Skills.json") ?? [];
			return serializer.ToString(skills);
		}

		// Handle Subscription Plans
		if (path.Contains("/api/SubscriptionPlan"))
		{
			var subscriptionPlans = await LoadData<List<SubscriptionPlanData>>("SubscriptionPlans.json") ?? [];
			return serializer.ToString(subscriptionPlans);
		}

		// Handle Token Transactions
		if (path.Contains("/api/TokenTransaction"))
		{
			var tokenTransactions = await LoadData<List<TokenTransactionData>>("TokenTransactions.json") ?? [];
			
			// Handle filtering by user ID if provided
			var query = request.RequestUri?.Query;
			if (!string.IsNullOrEmpty(query) && query.Contains("userId"))
			{
				var userId = ExtractUserIdFromQuery(query);
				if (Guid.TryParse(userId, out var parsedUserId))
				{
					tokenTransactions = tokenTransactions.Where(t => t.UserId == parsedUserId).ToList();
				}
			}

			// Handle filtering by transaction type if provided
			if (!string.IsNullOrEmpty(query) && query.Contains("type"))
			{
				var type = ExtractParameterFromQuery(query, "type");
				if (!string.IsNullOrEmpty(type))
				{
					tokenTransactions = tokenTransactions.Where(t => 
						string.Equals(t.Type, type, StringComparison.OrdinalIgnoreCase)).ToList();
				}
			}

			return serializer.ToString(tokenTransactions);
		}

		return "{}";
	}

	private static string? ExtractUserIdFromQuery(string queryParams)
	{
		var queryDictionary = System.Web.HttpUtility.ParseQueryString(queryParams);
		return queryDictionary["userId"];
	}

	private static string? ExtractParameterFromQuery(string queryParams, string parameterName)
	{
		var queryDictionary = System.Web.HttpUtility.ParseQueryString(queryParams);
		return queryDictionary[parameterName];
	}
}
